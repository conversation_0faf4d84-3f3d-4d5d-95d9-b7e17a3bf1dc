"""
Refactored Main Window for Excel Translator Application.
"""

from PyQt6.QtWidgets import QMainWindow
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QAction, QKeySequence

from ..managers.theme_manager import ThemeManager
from ..managers.localization_manager import get_localization_manager, tr
from ..builders.main_window_ui_builder import MainWindowUIBuilder
from ..controllers.main_window_controller import MainWindowController
from ..controllers.translation_controller import TranslationController
from .preferences_dialog import PreferencesDialog


class MainWindow(QMainWindow):
    """Main application window with refactored architecture."""
    
    def __init__(self, settings=None, excel_handler=None, google_translator=None, 
                 deepl_translator=None, logger=None):
        """Initialize the main window with dependency injection."""
        super().__init__()
        
        self.settings = settings
        self.excel_handler = excel_handler
        self.google_translator = google_translator
        self.deepl_translator = deepl_translator
        self.logger = logger

        # Initialize localization manager
        self.localization_manager = get_localization_manager()

        # Initialize business logic logger
        self.business_logger = None
        
        # Track loaded file data
        self.loaded_file_path = None
        self.loaded_file_cells = 0
        self.loaded_sheets = []
        
        # Initialize controllers and builders
        self.main_controller = MainWindowController(self)
        self.translation_controller = TranslationController(self)
        self.ui_builder = MainWindowUIBuilder(self)
        
        # Setup window
        self.setWindowTitle("Excel Translator")
        self.setFixedSize(1024, 768)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        
        # Setup theme and UI
        ThemeManager.setup_dark_palette(self)
        ThemeManager.setup_platform_dark_theme()
        self.setup_menu_bar()
        self.setup_ui()
        ThemeManager.apply_styles(self)
        self.connect_signals()

        # Connect localization signals
        self.localization_manager.language_changed.connect(self.update_ui_texts)

        # Restore language from settings
        self.restore_language_from_settings()

        # Log initialization (welcome message is now displayed by business logger)
        if self.logger:
            self.logger.info("Main window initialized with business logic")

            # Log translator service availability
            google_available = (self.google_translator and
                              hasattr(self.google_translator, 'client') and
                              self.google_translator.client is not None)
            deepl_available = (self.deepl_translator and
                             hasattr(self.deepl_translator, 'client') and
                             self.deepl_translator.client is not None)

            self.logger.info(f"Google Translate available: {google_available}")
            self.logger.info(f"DeepL available: {deepl_available}")

            if not google_available and not deepl_available:
                # Only show warning if API keys are configured but services failed to initialize
                has_google_key = bool(self.settings and self.settings.api.google_api_key)
                has_deepl_key = bool(self.settings and self.settings.api.deepl_api_key)

                if has_google_key or has_deepl_key:
                    self.logger.warning(
                        "No translation services are properly configured with valid API keys"
                    )
                else:
                    self.logger.info("No translation API keys configured - translation features disabled")

        # Progress bar will be set to proper initial state by UI builder
    
    def connect_signals(self):
        """Connect GUI signals to business logic handlers."""
        # File upload signals
        if hasattr(self, 'file_upload'):
            self.file_upload.file_selected.connect(
                self.main_controller.handle_file_upload
            )
        
        # Translation button signals - using new logic
        if hasattr(self, 'translate_button'):
            self.translate_button.clicked.connect(
                self.translation_controller.handle_excel_translate
            )
        
        # Cancel button signals
        if hasattr(self, 'cancel_button'):
            self.cancel_button.clicked.connect(
                self.translation_controller.handle_cancel_translation
            )
        
        # Export button signals
        if hasattr(self, 'export_button'):
            self.export_button.clicked.connect(
                self.main_controller.handle_export
            )
        
        # Language combo box signals
        if hasattr(self, 'source_language'):
            self.source_language.currentTextChanged.connect(
                self.main_controller.handle_source_language_change
            )
        
        if hasattr(self, 'target_language'):
            self.target_language.currentTextChanged.connect(
                self.main_controller.handle_target_language_change
            )
        
        # Translator combo box signals
        if hasattr(self, 'translator_combo'):
            self.translator_combo.currentTextChanged.connect(
                self.main_controller.handle_translator_change
            )
        
        # Batch size input signals
        if hasattr(self, 'batch_size_input'):
            self.batch_size_input.batch_size_changed.connect(
                self.main_controller.handle_batch_size_change
            )

    def setup_menu_bar(self):
        """Setup the menu bar with preferences menu."""
        # Only create menu if it doesn't exist
        if hasattr(self, 'preferences_menu') and self.preferences_menu:
            return

        menubar = self.menuBar()

        # Create Preferences menu
        preferences_menu = menubar.addMenu(tr("preferences"))

        # Settings action with keyboard shortcuts
        settings_action = QAction(self._get_settings_text_with_shortcut(), self)
        settings_action.triggered.connect(self.show_preferences_dialog)

        # Add keyboard shortcuts: Cmd+, (Mac) and Ctrl+, (Windows/Linux)
        import platform
        if platform.system() == "Darwin":  # macOS
            shortcut = QKeySequence("Cmd+,")
        else:  # Windows/Linux
            shortcut = QKeySequence("Ctrl+,")

        settings_action.setShortcut(shortcut)
        settings_action.setShortcutContext(Qt.ShortcutContext.ApplicationShortcut)
        settings_action.setMenuRole(QAction.MenuRole.PreferencesRole)

        preferences_menu.addAction(settings_action)

        # Store references for updating texts
        self.preferences_menu = preferences_menu
        self.settings_action = settings_action

    def _get_settings_text_with_shortcut(self):
        """Get settings text with keyboard shortcut displayed."""
        import platform
        if platform.system() == "Darwin":  # macOS
            shortcut_text = "⌘,"
        else:  # Windows/Linux
            shortcut_text = "Ctrl+,"

        return f"{tr('settings_title')}\t{shortcut_text}"

    def setup_ui(self):
        """Setup the main UI using the UI builder."""
        self.ui_builder.setup_central_widget()

    def show_preferences_dialog(self):
        """Show the preferences dialog."""
        google_key = self.settings.api.google_api_key if self.settings else ""
        deepl_key = self.settings.api.deepl_api_key if self.settings else ""

        dialog = PreferencesDialog(self, google_key, deepl_key)
        dialog.language_changed.connect(self.handle_language_change)
        dialog.api_keys_updated.connect(self.handle_api_keys_update)
        dialog.file_settings_updated.connect(self.handle_file_settings_update)
        dialog.translation_settings_updated.connect(self.handle_translation_settings_update)
        dialog.exec()

    def handle_language_change(self, language_code):
        """Handle language change from preferences."""
        # Update localization manager language
        self.localization_manager.set_language(language_code)

        if self.settings:
            self.settings.ui.language = language_code

        # Save settings to persist language change
        self.save_settings()

        # Update all UI texts including menu
        self.update_ui_texts()

        if self.logger:
            self.logger.info(f"Language changed to: {language_code}")

    def handle_api_keys_update(self, google_key, deepl_key):
        """Handle API keys update from preferences."""
        if not self.settings:
            return

        # Update settings
        old_google_key = self.settings.api.google_api_key
        old_deepl_key = self.settings.api.deepl_api_key

        self.settings.api.google_api_key = google_key
        self.settings.api.deepl_api_key = deepl_key

        # Save settings to file for persistence
        self.save_settings()

        # Reinitialize translation services if keys changed
        if google_key != old_google_key:
            if google_key:
                from infrastructure.plugins.google_translator import GoogleTranslator
                try:
                    self.google_translator = GoogleTranslator(google_key)
                    if self.logger:
                        self.logger.info("Google Translator reinitialized with new API key")
                    if self.business_logger:
                        self.business_logger.log_info("Google Translator service updated")
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Failed to initialize Google Translator: {e}")
                    if self.business_logger:
                        self.business_logger.log_error("Failed to initialize Google Translator")
                    self.google_translator = None
            else:
                self.google_translator = None
                if self.logger:
                    self.logger.info("Google Translator disabled (no API key)")
                if self.business_logger:
                    self.business_logger.log_info("Google Translator disabled")

        if deepl_key != old_deepl_key:
            if deepl_key:
                from infrastructure.plugins.deepl_translator import DeepLTranslator
                try:
                    self.deepl_translator = DeepLTranslator(deepl_key)
                    if self.logger:
                        self.logger.info("DeepL Translator reinitialized with new API key")
                    if self.business_logger:
                        self.business_logger.log_info("DeepL Translator service updated")
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Failed to initialize DeepL Translator: {e}")
                    if self.business_logger:
                        self.business_logger.log_error("Failed to initialize DeepL Translator")
                    self.deepl_translator = None
            else:
                self.deepl_translator = None
                if self.logger:
                    self.logger.info("DeepL Translator disabled (no API key)")
                if self.business_logger:
                    self.business_logger.log_info("DeepL Translator disabled")

        # Update translator combo box if available
        if hasattr(self, 'translator_combo'):
            self.update_translator_combo_availability()

    def handle_file_settings_update(self, max_file_size_mb, supported_formats):
        """Handle file settings update from preferences."""
        if not self.settings:
            # Create default settings if none exist
            from config.settings import ApplicationSettings
            self.settings = ApplicationSettings()
            if self.logger:
                self.logger.info("Created default settings for file settings update")

        # Update settings
        old_max_size = self.settings.file.max_file_size_mb
        old_formats = self.settings.file.supported_formats

        self.settings.file.max_file_size_mb = max_file_size_mb
        self.settings.file.supported_formats = supported_formats

        # Save settings to file for persistence
        self.save_settings()

        # Update .env file as well
        try:
            from config.env_manager import get_env_manager
            env_manager = get_env_manager()
            env_manager.update_file_settings(max_file_size_mb, supported_formats)
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not update .env file: {e}")

        # Log the changes
        if self.logger:
            self.logger.info(f"File settings updated: max_size={max_file_size_mb}MB (was {old_max_size}MB), formats={supported_formats}")

        if self.business_logger:
            self.business_logger.log_info(f"File settings updated: max size {max_file_size_mb}MB, formats {', '.join(supported_formats)}")

    def handle_translation_settings_update(self, translate_headers):
        """Handle translation settings update from preferences."""
        if not self.settings:
            # Create default settings if none exist
            from config.settings import ApplicationSettings
            self.settings = ApplicationSettings()
            if self.logger:
                self.logger.info("Created default settings for translation settings update")

        # Update settings
        old_translate_headers = self.settings.translation.translate_headers
        self.settings.translation.translate_headers = translate_headers

        # Save settings to file for persistence
        self.save_settings()

        # Log the changes
        if self.logger:
            self.logger.info(f"Translation settings updated: translate_headers={translate_headers} (was {old_translate_headers})")

        if self.business_logger:
            header_status = "enabled" if translate_headers else "disabled"
            self.business_logger.log_info(f"Header translation {header_status}")

    def restore_language_from_settings(self):
        """Restore language from settings on startup."""
        if self.settings and hasattr(self.settings.ui, 'language'):
            saved_language = self.settings.ui.language
            if saved_language and saved_language != self.localization_manager.current_language:
                # Set the language in localization manager
                if self.localization_manager.set_language(saved_language):
                    if self.logger:
                        self.logger.info(f"Restored language from settings: {saved_language}")
                else:
                    if self.logger:
                        self.logger.warning(f"Failed to restore language: {saved_language}")

    def save_settings(self):
        """Save current settings to file."""
        if self.settings:
            settings_file = "config/app_settings.json"
            try:
                if self.settings.save_to_file(settings_file):
                    if self.logger:
                        self.logger.info("Settings saved successfully")
                else:
                    if self.logger:
                        self.logger.warning("Failed to save settings")
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error saving settings: {e}")

    def update_translator_combo_availability(self):
        """Update translator combo box based on available services."""
        if not hasattr(self, 'translator_combo'):
            return

        # Clear current items
        self.translator_combo.clear()

        # Add available translators
        if self.deepl_translator and hasattr(self.deepl_translator, 'client') and self.deepl_translator.client:
            self.translator_combo.addItem("DeepL Translator")

        if self.google_translator and hasattr(self.google_translator, 'client') and self.google_translator.client:
            self.translator_combo.addItem("Google Translator")

        # Set default selection
        if self.translator_combo.count() > 0:
            self.translator_combo.setCurrentIndex(0)

    def update_ui_texts(self):
        """Update all UI texts when language changes."""
        # Update window title
        self.setWindowTitle(tr("app_title"))

        # Update menu texts
        if hasattr(self, 'preferences_menu'):
            self.preferences_menu.setTitle(tr("preferences"))
        if hasattr(self, 'settings_action'):
            self.settings_action.setText(self._get_settings_text_with_shortcut())

        # Update UI builder texts
        if hasattr(self, 'ui_builder'):
            self.ui_builder.update_ui_texts()
