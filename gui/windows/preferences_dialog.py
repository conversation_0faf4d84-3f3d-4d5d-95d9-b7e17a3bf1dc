"""
Preferences Dialog for language switching and API key management.
"""

from PyQt6.QtWidgets import (Q<PERSON><PERSON><PERSON>, Q<PERSON>oxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QComboBox, QLineEdit, QPushButton,
                            QMessageBox, QSpacerItem, QSizePolicy, QGroupBox, QCheckBox)
from ..components.input.file_size_input import FileSizeInput
from ..styles.dialog_style import MESSAGE_BOX_STYLE
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..managers.localization_manager import get_localization_manager, tr
from ..styles.translator_combo_box_style import (
    TRANSLATOR_COMBO_BOX_STYLE,
    TRANSLATOR_COMBO_BOX_POPUP_STYLE,
    TRANSLATOR_COMBO_BOX_LIST_STYLE
)


class PreferencesDialog(QDialog):
    """Preferences dialog with language and API key settings."""
    
    # Signals
    language_changed = pyqtSignal(str)  # language_code
    api_keys_updated = pyqtSignal(str, str)  # google_key, deepl_key
    file_settings_updated = pyqtSignal(float, list)  # max_file_size_mb, supported_formats
    translation_settings_updated = pyqtSignal(bool)  # translate_headers
    
    def __init__(self, parent=None, current_google_key="", current_deepl_key=""):
        """Initialize the preferences dialog."""
        super().__init__(parent)
        self.localization_manager = get_localization_manager()
        self.current_google_key = current_google_key
        self.current_deepl_key = current_deepl_key
        
        self.setup_ui()
        self.apply_styles()
        self.connect_signals()
        self.update_texts()
    
    def setup_ui(self):
        """Setup the dialog UI."""
        self.setWindowTitle(tr("preferences_title"))
        self.setFixedSize(550, 450)
        self.setModal(True)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # Title with better styling
        title_label = QLabel(tr("preferences_title"))
        title_label.setObjectName("preferencesMainTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Language tab
        self.language_tab = self.create_language_tab()
        self.tab_widget.addTab(self.language_tab, tr("language_tab"))
        
        # API tab
        self.api_tab = self.create_api_tab()
        self.tab_widget.addTab(self.api_tab, tr("api_tab"))

        # Translation Settings tab
        self.translation_tab = self.create_translation_settings_tab()
        self.tab_widget.addTab(self.translation_tab, tr("translation_tab"))

        # File Settings tab
        self.file_tab = self.create_file_settings_tab()
        self.tab_widget.addTab(self.file_tab, tr("file_tab"))

        # Buttons with better layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton(tr("cancel_settings"))
        self.cancel_button.setObjectName("preferencesButton")
        self.cancel_button.clicked.connect(self.reject)
        
        self.ok_button = QPushButton(tr("ok"))
        self.ok_button.setObjectName("preferencesButtonPrimary")
        self.ok_button.clicked.connect(self.accept)
        self.ok_button.setDefault(True)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)
        
        main_layout.addLayout(button_layout)
    
    def create_language_tab(self):
        """Create the language settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # UI Language section
        ui_lang_label = QLabel(tr("ui_language"))
        ui_lang_label.setObjectName("preferencesLabel")
        layout.addWidget(ui_lang_label)

        self.language_combo = QComboBox()
        self.language_combo.setFixedHeight(35)  # Same height as main window combos

        self.populate_language_combo()

        # Apply main window combo styling
        self.language_combo.setStyleSheet(TRANSLATOR_COMBO_BOX_STYLE)
        popup = self.language_combo.view().window()
        popup.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint | Qt.WindowType.NoDropShadowWindowHint)
        popup.setStyleSheet(TRANSLATOR_COMBO_BOX_POPUP_STYLE)
        self.language_combo.view().setContentsMargins(0, 0, 0, 0)
        self.language_combo.view().setFrameStyle(0)
        self.language_combo.view().setStyleSheet(TRANSLATOR_COMBO_BOX_LIST_STYLE)

        layout.addWidget(self.language_combo)

        # Spacer
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

        return tab

    def populate_language_combo(self):
        """Populate the language combo box with current language names."""
        # Store current selection
        current_lang = self.localization_manager.current_language

        # Temporarily disconnect signals to avoid recursion
        try:
            self.language_combo.activated.disconnect(self.on_language_changed)
        except TypeError:
            # Signal wasn't connected yet, that's fine
            pass

        # Clear and repopulate with localized language names
        self.language_combo.clear()
        supported_languages = self.localization_manager.supported_languages
        for code, name in supported_languages.items():
            self.language_combo.addItem(name, code)

        # Restore selection
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == current_lang:
                self.language_combo.setCurrentIndex(i)
                break

        # Reconnect signals
        self.language_combo.activated.connect(self.on_language_changed)
    
    def create_api_tab(self):
        """Create the API settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Google API Key section
        google_label = QLabel(tr("google_api_key"))
        google_label.setObjectName("preferencesLabel")
        layout.addWidget(google_label)
        
        self.google_key_input = QLineEdit()
        self.google_key_input.setObjectName("preferencesInput")
        self.google_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.google_key_input.setText(self.current_google_key)
        self.google_key_input.setPlaceholderText("Enter Google Translate API key...")
        layout.addWidget(self.google_key_input)
        
        # DeepL API Key section
        deepl_label = QLabel(tr("deepl_api_key"))
        deepl_label.setObjectName("preferencesLabel")
        layout.addWidget(deepl_label)
        
        self.deepl_key_input = QLineEdit()
        self.deepl_key_input.setObjectName("preferencesInput")
        self.deepl_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.deepl_key_input.setText(self.current_deepl_key)
        self.deepl_key_input.setPlaceholderText("Enter DeepL API key...")
        layout.addWidget(self.deepl_key_input)
        
        # Apply button for instant updates
        apply_layout = QHBoxLayout()
        apply_layout.addStretch()
        
        self.apply_button = QPushButton(tr("apply"))
        self.apply_button.setObjectName("preferencesApplyButton")
        apply_layout.addWidget(self.apply_button)
        
        layout.addLayout(apply_layout)
        
        # Spacer
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        
        return tab

    def create_file_settings_tab(self):
        """Create the file settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Maximum File Size section
        max_size_label = QLabel(tr("max_file_size"))
        max_size_label.setObjectName("preferencesLabel")
        layout.addWidget(max_size_label)

        self.max_file_size_input = FileSizeInput()
        # Keep the batch size styling (with custom arrows) - don't set objectName to avoid preferences styling

        # Get initial value safely
        initial_max_size = 50  # Default value
        if self.parent() and hasattr(self.parent(), 'settings') and self.parent().settings:
            initial_max_size = int(self.parent().settings.file.max_file_size_mb)

        self.max_file_size_input.set_value_mb(initial_max_size)
        layout.addWidget(self.max_file_size_input)

        # Supported File Formats section
        formats_label = QLabel(tr("supported_formats"))
        formats_label.setObjectName("preferencesLabel")
        layout.addWidget(formats_label)

        self.supported_formats_edit = QLineEdit()
        self.supported_formats_edit.setObjectName("preferencesInput")

        # Get initial formats safely
        initial_formats = "xlsx,xls"  # Default value
        if self.parent() and hasattr(self.parent(), 'settings') and self.parent().settings:
            current_formats = [fmt.lstrip('.') for fmt in self.parent().settings.file.supported_formats]
            initial_formats = ','.join(current_formats)

        self.supported_formats_edit.setText(initial_formats)
        self.supported_formats_edit.setPlaceholderText("Enter supported formats (e.g., xlsx,xls,csv)...")
        layout.addWidget(self.supported_formats_edit)

        # Apply button for instant updates
        apply_layout = QHBoxLayout()
        apply_layout.addStretch()

        self.apply_file_button = QPushButton(tr("apply"))
        self.apply_file_button.setObjectName("preferencesApplyButton")
        self.apply_file_button.clicked.connect(self.on_apply_file_settings)
        apply_layout.addWidget(self.apply_file_button)

        layout.addLayout(apply_layout)

        # Spacer
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

        return tab

    def create_translation_settings_tab(self):
        """Create the translation settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Translation Options section
        options_label = QLabel(tr("translation_options"))
        options_label.setObjectName("preferencesLabel")
        layout.addWidget(options_label)

        # Translate Headers checkbox
        self.translate_headers_checkbox = QCheckBox(tr("translate_headers"))
        self.translate_headers_checkbox.setObjectName("preferencesCheckBox")

        # Get initial value safely
        initial_translate_headers = True  # Default value
        if self.parent() and hasattr(self.parent(), 'settings') and self.parent().settings:
            initial_translate_headers = self.parent().settings.translation.translate_headers

        self.translate_headers_checkbox.setChecked(initial_translate_headers)
        layout.addWidget(self.translate_headers_checkbox)

        # Apply button for instant updates
        apply_layout = QHBoxLayout()
        apply_layout.addStretch()

        self.apply_translation_button = QPushButton(tr("apply"))
        self.apply_translation_button.setObjectName("preferencesApplyButton")
        self.apply_translation_button.clicked.connect(self.on_apply_translation_settings)
        apply_layout.addWidget(self.apply_translation_button)

        layout.addLayout(apply_layout)

        # Spacer
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

        return tab

    def connect_signals(self):
        """Connect signals to slots."""
        # Use activated signal only to avoid recursion from programmatic changes
        self.language_combo.activated.connect(self.on_language_changed)
        self.apply_button.clicked.connect(self.on_apply_api_keys)

        # Connect to localization manager's language_changed signal to update all tabs
        # This ensures that when language changes, all tabs get updated immediately
        self.localization_manager.language_changed.connect(self.update_texts)
    
    def on_language_changed(self):
        """Handle language change from combo box selection."""
        selected_code = self.language_combo.currentData()
        current_language = self.localization_manager.current_language

        if selected_code and selected_code != current_language:
            # Temporarily disconnect our own signal to prevent recursion
            try:
                self.localization_manager.language_changed.disconnect(self.update_texts)
            except TypeError:
                pass

            # Set language in localization manager (this will trigger main window updates)
            self.localization_manager.set_language(selected_code)

            # Emit signal to main window for additional handling
            self.language_changed.emit(selected_code)

            # Update dialog texts manually
            self.update_texts()

            # Reconnect our signal
            self.localization_manager.language_changed.connect(self.update_texts)
    
    def on_apply_api_keys(self):
        """Handle API key updates."""
        google_key = self.google_key_input.text().strip()
        deepl_key = self.deepl_key_input.text().strip()

        self.api_keys_updated.emit(google_key, deepl_key)

        # Show styled confirmation dialog (same as export confirmation)
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(tr("preferences_title"))
        msg_box.setText(tr("api_key_saved"))
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
        msg_box.exec()

    def on_apply_file_settings(self):
        """Apply file settings changes."""
        try:
            # Get values from UI
            max_file_size = float(self.max_file_size_input.get_value_mb())
            formats_text = self.supported_formats_edit.text().strip()

            # Parse supported formats
            if formats_text:
                # Split by comma and clean up
                formats_list = [f'.{fmt.strip().lstrip(".")}' for fmt in formats_text.split(',') if fmt.strip()]
            else:
                formats_list = ['.xlsx', '.xls']  # Default formats

            # Emit signal first (main window will handle settings update)
            self.file_settings_updated.emit(max_file_size, formats_list)

            # Show styled confirmation dialog (same as export confirmation)
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(tr("preferences_title"))
            msg_box.setText(tr("file_settings_saved"))
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
            msg_box.exec()

        except ValueError as e:
            # Show styled warning dialog
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(tr("preferences_title"))
            msg_box.setText(f"Invalid input: {e}")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
            msg_box.exec()
        except Exception as e:
            # Show styled error dialog
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(tr("preferences_title"))
            msg_box.setText(f"Error saving file settings: {e}")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
            msg_box.exec()

    def on_apply_translation_settings(self):
        """Apply translation settings changes."""
        try:
            # Get values from UI
            translate_headers = self.translate_headers_checkbox.isChecked()

            # Emit signal (main window will handle settings update)
            self.translation_settings_updated.emit(translate_headers)

            # Show styled confirmation dialog
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(tr("preferences_title"))
            msg_box.setText(tr("translation_settings_saved"))
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
            msg_box.exec()

        except Exception as e:
            # Show styled error dialog
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(tr("preferences_title"))
            msg_box.setText(f"Error saving translation settings: {e}")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setStyleSheet(MESSAGE_BOX_STYLE)
            msg_box.exec()

    def closeEvent(self, event):
        """Handle dialog close event to clean up signals."""
        try:
            # Disconnect from localization manager to prevent memory leaks
            self.localization_manager.language_changed.disconnect(self.update_texts)
        except TypeError:
            # Signal wasn't connected, that's fine
            pass
        super().closeEvent(event)

    def update_texts(self):
        """Update all UI texts when language changes."""
        self.setWindowTitle(tr("preferences_title"))

        # Update main title label
        title_labels = self.findChildren(QLabel, "preferencesMainTitle")
        for label in title_labels:
            label.setText(tr("preferences_title"))

        # Update tab titles
        self.tab_widget.setTabText(0, tr("language_tab"))
        self.tab_widget.setTabText(1, tr("api_tab"))
        self.tab_widget.setTabText(2, tr("translation_tab"))
        self.tab_widget.setTabText(3, tr("file_tab"))

        # Update button texts
        self.ok_button.setText(tr("ok"))
        self.cancel_button.setText(tr("cancel_settings"))
        self.apply_button.setText(tr("apply"))
        if hasattr(self, 'apply_file_button'):
            self.apply_file_button.setText(tr("apply"))
        if hasattr(self, 'apply_translation_button'):
            self.apply_translation_button.setText(tr("apply"))

        # Update language combo box items with localized names
        self.populate_language_combo()

        # Update all labels in all tabs
        self._update_language_tab_texts()
        self._update_api_tab_texts()
        self._update_translation_tab_texts()
        self._update_file_tab_texts()

        # Update input placeholders
        self._update_input_placeholders()

    def _update_language_tab_texts(self):
        """Update texts in the Language tab."""
        # Find and update the UI language label
        for label in self.language_tab.findChildren(QLabel):
            if label.objectName() == "preferencesLabel":
                label.setText(tr("ui_language"))

    def _update_api_tab_texts(self):
        """Update texts in the API tab."""
        # Find all labels in API tab and update them based on their content or position
        labels = self.api_tab.findChildren(QLabel)
        preference_labels = [label for label in labels if label.objectName() == "preferencesLabel"]

        # Update labels based on their order in the layout
        if len(preference_labels) >= 2:
            preference_labels[0].setText(tr("google_api_key"))  # First label
            preference_labels[1].setText(tr("deepl_api_key"))   # Second label
        elif len(preference_labels) == 1:
            # If only one label, check its current text to determine which one it is
            current_text = preference_labels[0].text().lower()
            if "google" in current_text or "Google" in preference_labels[0].text():
                preference_labels[0].setText(tr("google_api_key"))
            else:
                preference_labels[0].setText(tr("deepl_api_key"))

    def _update_translation_tab_texts(self):
        """Update texts in the Translation Settings tab."""
        # Find all labels in Translation tab and update them
        labels = self.translation_tab.findChildren(QLabel)
        preference_labels = [label for label in labels if label.objectName() == "preferencesLabel"]

        # Update the translation options label
        if len(preference_labels) >= 1:
            preference_labels[0].setText(tr("translation_options"))

        # Update checkbox text
        if hasattr(self, 'translate_headers_checkbox'):
            self.translate_headers_checkbox.setText(tr("translate_headers"))

    def _update_file_tab_texts(self):
        """Update texts in the File Settings tab."""
        # Find all labels in File tab and update them based on their content or position
        labels = self.file_tab.findChildren(QLabel)
        preference_labels = [label for label in labels if label.objectName() == "preferencesLabel"]

        # Update labels based on their order in the layout
        if len(preference_labels) >= 2:
            preference_labels[0].setText(tr("max_file_size"))      # First label
            preference_labels[1].setText(tr("supported_formats"))  # Second label
        elif len(preference_labels) == 1:
            # If only one label, check its current text to determine which one it is
            current_text = preference_labels[0].text().lower()
            if "size" in current_text or "サイズ" in preference_labels[0].text():
                preference_labels[0].setText(tr("max_file_size"))
            else:
                preference_labels[0].setText(tr("supported_formats"))

    def _update_input_placeholders(self):
        """Update placeholder texts for input fields."""
        # Update API key input placeholders
        if hasattr(self, 'google_key_input'):
            self.google_key_input.setPlaceholderText("Enter Google Translate API key...")
        if hasattr(self, 'deepl_key_input'):
            self.deepl_key_input.setPlaceholderText("Enter DeepL API key...")

        # Update file settings input placeholders
        if hasattr(self, 'supported_formats_edit'):
            self.supported_formats_edit.setPlaceholderText("Enter supported formats (e.g., xlsx,xls,csv)...")
    
    def apply_styles(self):
        """Apply midnight dark theme styles."""
        self.setStyleSheet("""
            QDialog {
                background-color: #0d1117;
                color: #f0f6fc;
            }
            
            QLabel#preferencesMainTitle {
                font-size: 18px;
                font-weight: normal;
                color: #f0f6fc;
                margin-bottom: 10px;
                padding: 10px;
            }
            
            QTabWidget::pane {
                border: 1px solid #30363d;
                background-color: #0d1117;
                border-radius: 6px;
            }
            
            QTabWidget::tab-bar {
                alignment: center;
            }
            
            QTabBar::tab {
                background-color: #21262d;
                color: #8b949e;
                border: 1px solid #30363d;
                border-bottom: none;
                border-radius: 6px 6px 0px 0px;
                padding: 10px 20px;
                margin-right: 2px;
                font-size: 14px;
                font-weight: 500;
                min-width: 80px;
            }
            
            QTabBar::tab:selected {
                background-color: #0d1117;
                color: #f0f6fc;
                border-color: #30363d;
            }
            
            QTabBar::tab:hover {
                background-color: #30363d;
                color: #f0f6fc;
            }
            
            QLabel#preferencesLabel {
                font-size: 14px;
                font-weight: normal;
                color: #f0f6fc;
                margin-bottom: 5px;
                margin-top: 5px;
            }
            
            QLineEdit#preferencesInput {
                background-color: #21262d;
                border: 1px solid #30363d;
                border-radius: 6px;
                color: #f0f6fc;
                font-size: 13px;
                padding: 10px 12px;
                min-height: 20px;
            }
            
            QLineEdit#preferencesInput:hover {
                border-color: #484f58;
            }

            QLineEdit#preferencesInput:focus {
                border-color: #666666;
                outline: none;
                background-color: #161b22;
            }


            
            QPushButton#preferencesButton {
                background-color: #21262d;
                border: 1px solid #30363d;
                border-radius: 6px;
                color: #f0f6fc;
                font-size: 13px;
                padding: 10px 20px;
                min-width: 90px;
                font-weight: normal;
            }
            
            QPushButton#preferencesButton:hover {
                background-color: #30363d;
                border-color: #484f58;
            }
            
            QPushButton#preferencesButton:pressed {
                background-color: #161b22;
            }
            
            QPushButton#preferencesButtonPrimary {
                background-color: #21262d;
                border: 1px solid #238636;
                border-radius: 6px;
                color: #f0f6fc;
                font-size: 13px;
                font-weight: normal;
                padding: 12px 24px;
                min-width: 120px;
            }

            QPushButton#preferencesButtonPrimary:hover {
                background-color: #30363d;
                border: 2px solid #2ea043;
            }

            QPushButton#preferencesButtonPrimary:pressed {
                background-color: #161b22;
                border: 2px solid #238636;
            }
            
            QPushButton#preferencesApplyButton {
                background-color: #21262d;
                border: 1px solid #30363d;
                border-radius: 6px;
                color: #ffffff;
                font-size: 13px;
                font-weight: normal;
                padding: 8px 16px;
                min-width: 80px;
            }

            QPushButton#preferencesApplyButton:hover {
                background-color: #30363d;
                border-color: #00ccff;
            }

            QPushButton#preferencesApplyButton:pressed {
                background-color: #161b22;
            }

            QCheckBox#preferencesCheckBox {
                color: #f0f6fc;
                font-size: 13px;
                spacing: 8px;
                background-color: transparent;
                padding: 6px 4px;
                border-radius: 4px;
            }

            QCheckBox#preferencesCheckBox:hover {
                background-color: #161b22;
            }

            QCheckBox#preferencesCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #30363d;
                border-radius: 3px;
                background-color: #0d1117;
            }

            QCheckBox#preferencesCheckBox::indicator:hover {
                border-color: #484f58;
                background-color: #21262d;
            }

            QCheckBox#preferencesCheckBox::indicator:checked {
                background-color: #30363d;
                border-color: #30363d;
            }

            QCheckBox#preferencesCheckBox::indicator:checked:hover {
                background-color: #484f58;
                border-color: #484f58;
            }
        """)
