#!/usr/bin/env python3
"""
Test translation with large datasets and long sentences
"""

import sys
from pathlib import Path
import asyncio
import pandas as pd
import time

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from infrastructure.plugins.deepl_translator import DeepLTranslator
from domain.entities.translation import TranslationRequest
from infrastructure.file_handlers.excel_handler import ExcelHandler

async def create_large_test_file():
    """Create a large Excel file with various content types"""
    print("=== Creating Large Test Dataset ===")
    
    # Long sentences for testing
    long_sentences = [
        "This is a comprehensive product description that contains multiple technical specifications, usage instructions, and detailed information about the manufacturing process, quality control measures, and customer support services.",
        "Our advanced software solution provides enterprise-level functionality including real-time data analytics, automated reporting capabilities, seamless integration with existing systems, and comprehensive security features to protect sensitive business information.",
        "The customer service department handles inquiries, complaints, technical support requests, billing questions, and provides assistance with product installation, configuration, troubleshooting, and maintenance procedures for all our products and services.",
        "This detailed technical documentation covers installation procedures, configuration settings, troubleshooting guides, API reference materials, best practices for implementation, performance optimization techniques, and security considerations for system administrators.",
        "Our comprehensive training program includes hands-on workshops, online tutorials, certification courses, practical exercises, real-world case studies, and ongoing support to ensure successful implementation and adoption of our solutions."
    ]
    
    # Short phrases for variety
    short_phrases = [
        "Product Name", "Description", "Category", "Price", "Stock", "Manufacturer",
        "Available", "Out of Stock", "In Transit", "Discontinued", "New Release",
        "Electronics", "Clothing", "Books", "Home & Garden", "Sports & Outdoors",
        "Customer Service", "Technical Support", "Sales Department", "Marketing Team"
    ]
    
    # Create large dataset
    data = []
    
    # Add headers with long descriptions
    headers = [
        "Product Identification Number and Primary Key",
        "Comprehensive Product Description and Technical Specifications",
        "Product Category and Classification Information",
        "Current Market Price Including Taxes and Fees",
        "Available Stock Quantity and Inventory Status",
        "Manufacturer Information and Contact Details"
    ]
    
    # Generate 500 rows of test data
    print("Generating 500 rows of test data...")
    for i in range(500):
        row = [
            f"PROD-{i+1:04d}",  # Product ID
            long_sentences[i % len(long_sentences)],  # Long description
            short_phrases[i % len(short_phrases)],  # Category
            f"${(i * 1.5 + 10):.2f}",  # Price
            f"{(i * 3 + 50) % 1000}",  # Stock
            f"Manufacturer Company {(i % 10) + 1} with comprehensive quality assurance and customer support services"  # Manufacturer
        ]
        data.append(row)
    
    # Create DataFrame and save to Excel
    df = pd.DataFrame(data, columns=headers)
    test_file_path = "large_test_dataset.xlsx"
    df.to_excel(test_file_path, index=False)
    
    print(f"✓ Created {test_file_path}")
    print(f"  - {len(df)} rows")
    print(f"  - {len(df.columns)} columns")
    print(f"  - File size: {Path(test_file_path).stat().st_size / 1024:.1f} KB")
    
    # Show sample data
    print("\nSample data:")
    for i, col in enumerate(headers[:3]):
        print(f"  Column {i+1}: '{col}'")
        print(f"    Sample: '{df.iloc[0, i][:80]}...'")
    
    return test_file_path

async def test_large_dataset_translation():
    """Test translation with large dataset"""
    print("\n=== Testing Large Dataset Translation ===")
    
    # Create test file
    test_file_path = await create_large_test_file()
    
    # Initialize components
    translator = DeepLTranslator()
    excel_handler = ExcelHandler()
    
    if not translator.client:
        print("ERROR: DeepL translator not available")
        return
    
    print(f"✓ DeepL translator initialized")
    
    # Load Excel file
    start_time = time.time()
    excel_file = await excel_handler.load_file(Path(test_file_path))
    load_time = time.time() - start_time
    
    if not excel_file or not excel_file.is_valid:
        print("ERROR: Failed to load Excel file")
        return
    
    print(f"✓ File loaded in {load_time:.2f} seconds")
    
    # Get translatable data
    start_time = time.time()
    translatable_data = await excel_handler.get_translatable_text_with_formatting(excel_file)
    extract_time = time.time() - start_time
    
    print(f"✓ Found {len(translatable_data)} translatable cells in {extract_time:.2f} seconds")
    
    # Test different batch sizes
    batch_sizes = [10, 25, 50]
    
    for batch_size in batch_sizes:
        print(f"\n--- Testing Batch Size: {batch_size} ---")
        
        # Limit to first 100 cells for testing
        test_data = translatable_data[:100]
        
        start_time = time.time()
        translation_results = {}
        successful_translations = 0
        failed_translations = 0
        
        # Process in batches
        for i in range(0, len(test_data), batch_size):
            batch = test_data[i:i+batch_size]
            batch_num = (i // batch_size) + 1
            
            print(f"  Processing batch {batch_num} ({len(batch)} items)...")
            
            # Create requests
            requests = []
            for data in batch:
                request = TranslationRequest(
                    text=data['original_text'],
                    source_language="auto",
                    target_language="ja"
                )
                requests.append(request)
            
            # Translate batch
            try:
                results = await translator.translate_batch(requests)
                
                # Process results
                for data, result in zip(batch, results):
                    if result and not result.error_message and result.translated_text and result.translated_text.strip():
                        translation_results[data['original_text']] = result.translated_text
                        successful_translations += 1
                    else:
                        translation_results[data['original_text']] = data['original_text']
                        failed_translations += 1
                        
            except Exception as e:
                print(f"    ERROR in batch {batch_num}: {e}")
                for data in batch:
                    translation_results[data['original_text']] = data['original_text']
                    failed_translations += 1
        
        total_time = time.time() - start_time
        
        print(f"  ✓ Completed {len(test_data)} cells in {total_time:.2f} seconds")
        print(f"  ✓ Success rate: {successful_translations}/{len(test_data)} ({100*successful_translations/len(test_data):.1f}%)")
        print(f"  ✓ Average time per cell: {total_time/len(test_data):.3f} seconds")
        print(f"  ✓ Throughput: {len(test_data)/total_time:.1f} cells/second")
        
        # Show sample translations
        print("  Sample translations:")
        sample_count = 0
        for original, translated in translation_results.items():
            if original != translated and sample_count < 3:
                print(f"    '{original[:50]}...' -> '{translated[:50]}...'")
                sample_count += 1
        
        # Test export with this batch size
        output_path = Path(f"large_test_output_batch{batch_size}.xlsx")
        start_time = time.time()
        success = await excel_handler.apply_translations_with_formatting(
            excel_file,
            translation_results,
            output_path
        )
        export_time = time.time() - start_time
        
        if success:
            print(f"  ✓ Export completed in {export_time:.2f} seconds")
            print(f"  ✓ Output file: {output_path} ({Path(output_path).stat().st_size / 1024:.1f} KB)")
            
            # Quick verification
            try:
                df_exported = pd.read_excel(output_path)
                print(f"  ✓ Verification: {len(df_exported)} rows exported")
                
                # Check if headers were translated
                original_headers = pd.read_excel(test_file_path).columns.tolist()
                exported_headers = df_exported.columns.tolist()
                
                translated_headers = sum(1 for orig, exp in zip(original_headers, exported_headers) if orig != exp)
                print(f"  ✓ Headers translated: {translated_headers}/{len(original_headers)}")
                
            except Exception as e:
                print(f"  ✗ Verification failed: {e}")
        else:
            print(f"  ✗ Export failed")
    
    # Clean up
    print(f"\n=== Cleaning up test files ===")
    for file in [test_file_path] + [f"large_test_output_batch{bs}.xlsx" for bs in batch_sizes]:
        Path(file).unlink(missing_ok=True)
        print(f"✓ Removed {file}")

async def test_long_sentences():
    """Test translation of very long sentences"""
    print("\n=== Testing Long Sentence Translation ===")
    
    translator = DeepLTranslator()
    
    if not translator.client:
        print("ERROR: DeepL translator not available")
        return
    
    # Very long sentences of different types
    long_sentences = [
        # Technical documentation (500+ chars)
        "This comprehensive technical documentation provides detailed information about the installation procedures, configuration settings, system requirements, compatibility matrices, troubleshooting guides, maintenance schedules, security protocols, backup procedures, disaster recovery plans, performance optimization techniques, monitoring and alerting mechanisms, user access controls, data integrity verification methods, and ongoing support services available for our enterprise software solution.",
        
        # Legal text (400+ chars)
        "By using this software, you acknowledge and agree to be bound by the terms and conditions of this license agreement, including but not limited to the limitations of liability, warranty disclaimers, intellectual property rights, data protection policies, privacy considerations, compliance requirements, and dispute resolution procedures as outlined in the complete legal documentation.",
        
        # Product description (600+ chars)
        "Our revolutionary new product combines cutting-edge technology with user-friendly design to deliver an unprecedented level of performance, reliability, and functionality that exceeds industry standards and customer expectations while maintaining cost-effectiveness, environmental sustainability, and compatibility with existing systems, backed by comprehensive warranty coverage, dedicated customer support, extensive training resources, and continuous updates.",
        
        # Scientific text (450+ chars)
        "The research methodology employed in this study involves a systematic approach to data collection, statistical analysis, hypothesis testing, peer review processes, experimental design validation, control group establishment, variable isolation techniques, measurement accuracy verification, result reproducibility testing, and comprehensive documentation of all procedures to ensure scientific rigor and validity.",
        
        # Business process (350+ chars)
        "The customer onboarding process includes initial consultation, needs assessment, solution design, implementation planning, resource allocation, timeline establishment, milestone definition, progress monitoring, quality assurance testing, user training, system integration, and ongoing support to ensure successful adoption."
    ]
    
    print(f"Testing {len(long_sentences)} long sentences...")
    
    for i, sentence in enumerate(long_sentences, 1):
        print(f"\n--- Long Sentence Test {i} ---")
        print(f"Length: {len(sentence)} characters")
        print(f"Preview: '{sentence[:100]}...'")
        
        # Test English to Japanese
        start_time = time.time()
        request = TranslationRequest(
            text=sentence,
            source_language="en",
            target_language="ja"
        )
        
        result = await translator.translate_text(request)
        translation_time = time.time() - start_time
        
        if result and not result.error_message and result.translated_text:
            print(f"✓ Translation successful in {translation_time:.2f} seconds")
            print(f"  Original length: {len(sentence)} chars")
            print(f"  Translated length: {len(result.translated_text)} chars")
            print(f"  Translated preview: '{result.translated_text[:100]}...'")
            
            # Test reverse translation (Japanese to English)
            reverse_request = TranslationRequest(
                text=result.translated_text,
                source_language="ja",
                target_language="en"
            )
            
            reverse_result = await translator.translate_text(reverse_request)
            if reverse_result and not reverse_result.error_message:
                print(f"✓ Reverse translation successful")
                print(f"  Reverse preview: '{reverse_result.translated_text[:100]}...'")
            else:
                print(f"✗ Reverse translation failed: {reverse_result.error_message if reverse_result else 'No result'}")
                
        else:
            error = result.error_message if result else "No result returned"
            print(f"✗ Translation failed: {error}")

async def test_performance_limits():
    """Test performance with various data sizes"""
    print("\n=== Testing Performance Limits ===")
    
    translator = DeepLTranslator()
    if not translator.client:
        print("ERROR: DeepL translator not available")
        return
    
    # Test different data volumes
    test_sizes = [10, 50, 100, 200]
    
    for size in test_sizes:
        print(f"\n--- Testing {size} translations ---")
        
        # Generate test data
        test_texts = [
            f"This is test sentence number {i+1} containing some meaningful content that needs to be translated accurately and efficiently."
            for i in range(size)
        ]
        
        # Create requests
        requests = [
            TranslationRequest(text=text, source_language="en", target_language="ja")
            for text in test_texts
        ]
        
        # Test batch translation
        start_time = time.time()
        results = await translator.translate_batch(requests)
        batch_time = time.time() - start_time
        
        # Analyze results
        successful = sum(1 for r in results if r and not r.error_message and r.translated_text)
        failed = len(results) - successful
        
        print(f"  ✓ Batch translation: {batch_time:.2f} seconds")
        print(f"  ✓ Success rate: {successful}/{len(requests)} ({100*successful/len(requests):.1f}%)")
        print(f"  ✓ Throughput: {len(requests)/batch_time:.1f} translations/second")
        print(f"  ✓ Average per translation: {batch_time/len(requests):.3f} seconds")
        
        if failed > 0:
            print(f"  ⚠ Failed translations: {failed}")

if __name__ == "__main__":
    asyncio.run(test_large_dataset_translation())
    asyncio.run(test_long_sentences())
    asyncio.run(test_performance_limits())
