"""
Excel file handler implementation for processing Excel files.
"""

import async<PERSON>
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from pathlib import Path
import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.table import Table
import re
import time
import xml.etree.ElementTree as ET

from domain.entities.excel_file import ExcelFile, ExcelSheet
from domain.entities.formatting import Formatting, CellDimensions, FontProperties, BorderProperties, FillProperties
from interfaces.repositories.file_repository_interface import FileRepositoryInterface


class ExcelHandler(FileRepositoryInterface):
    """Implementation for handling Excel file operations."""
    
    def __init__(self):
        """Initialize the Excel handler."""
        self.supported_extensions = ['.xlsx', '.xls']
        self.text_pattern = re.compile(r'[^\s\d\W]', re.UNICODE)
        self.bracket_patterns = [
            re.compile(r'\[.*?\]'),
            re.compile(r'「.*?」'),
            re.compile(r'【.*?】'),
        ]
    
    async def load_file(self, file_path: Path) -> Optional[ExcelFile]:
        """Load an Excel file and return ExcelFile entity."""
        try:
            if not await self.validate_file(file_path):
                return ExcelFile(
                    file_path=file_path,
                    file_name=file_path.name,
                    file_size=0,
                    is_valid=False,
                    error_message="File validation failed"
                )

            file_info = await self.get_file_info(file_path)
            if not file_info:
                return ExcelFile(
                    file_path=file_path,
                    file_name=file_path.name,
                    file_size=0,
                    is_valid=False,
                    error_message="Could not get file information"
                )

            excel_file = ExcelFile(
                file_path=file_path,
                file_name=file_path.name,
                file_size=file_info['size'],
                is_valid=True
            )

            try:
                if file_path.suffix.lower() == '.xlsx':
                    workbook = openpyxl.load_workbook(file_path, data_only=False)
                else:
                    workbook = await self._load_xls_file(file_path)
                
                if not workbook:
                    excel_file.is_valid = False
                    excel_file.error_message = "Could not load workbook"
                    return excel_file
                
                # Process each sheet
                for sheet_index, sheet_name in enumerate(workbook.sheetnames):
                    worksheet = workbook[sheet_name]
                    excel_sheet = await self._process_sheet(worksheet, sheet_index)
                    excel_file.add_sheet(excel_sheet)
                
                return excel_file
                
            except Exception as e:
                excel_file.is_valid = False
                excel_file.error_message = f"Error loading workbook: {str(e)}"
                return excel_file
                
        except Exception as e:
            return ExcelFile(
                file_path=file_path,
                file_name=file_path.name,
                file_size=0,
                is_valid=False,
                error_message=f"Error loading file: {str(e)}"
            )
    
    async def save_file(self, excel_file: ExcelFile, output_path: Path) -> bool:
        """Save an Excel file to the specified path."""
        try:
            # Load the original workbook
            workbook = openpyxl.load_workbook(excel_file.file_path, data_only=False)
            
            # Apply any modifications if needed
            # This would include translated text and preserved formatting
            
            # Save to output path
            workbook.save(output_path)
            return True
            
        except Exception:
            return False
    
    async def validate_file(self, file_path: Path) -> bool:
        """Validate if the file is a valid Excel file."""
        try:
            if not file_path.exists():
                return False
            
            if file_path.suffix.lower() not in self.supported_extensions:
                return False
            
            if file_path.stat().st_size == 0:
                return False
            
            # Try to open the file to verify it's valid
            if file_path.suffix.lower() == '.xlsx':
                try:
                    workbook = openpyxl.load_workbook(file_path, read_only=True)
                    workbook.close()
                    return True
                except Exception:
                    return False
            else:
                # For .xls files
                try:
                    import xlrd
                    workbook = xlrd.open_workbook(file_path)
                    return True
                except Exception:
                    return False
                    
        except Exception:
            return False
    
    async def get_file_info(self, file_path: Path) -> Optional[dict]:
        """Get basic file information."""
        try:
            if not file_path.exists():
                return None
            
            stat = file_path.stat()
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'extension': file_path.suffix.lower()
            }
            
        except Exception:
            return None
    
    async def backup_file(self, file_path: Path, backup_dir: Path) -> bool:
        """Create a backup of the file."""
        try:
            if not file_path.exists():
                return False
            
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = int(time.time())
            backup_name = f"{file_path.stem}_backup_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name
            
            import shutil
            shutil.copy2(file_path, backup_path)
            return True
            
        except Exception:
            return False
    
    async def list_files(self, directory: Path, extensions: List[str]) -> List[Path]:
        """List files in directory with specified extensions."""
        try:
            if not directory.exists() or not directory.is_dir():
                return []
            
            files = []
            for ext in extensions:
                pattern = f"*{ext}"
                files.extend(directory.glob(pattern))
            
            return sorted(files)
            
        except Exception:
            return []
    
    async def _process_sheet(self, worksheet, sheet_index: int) -> ExcelSheet:
        """Process a worksheet and extract translatable content."""
        sheet_name = worksheet.title
        
        # Get sheet dimensions
        max_row = worksheet.max_row or 0
        max_col = worksheet.max_column or 0
        
        # Find translatable cells and check for any data
        translatable_cells = []
        has_any_data = False

        for row in range(1, max_row + 1):
            for col in range(1, max_col + 1):
                cell = worksheet.cell(row=row, column=col)

                # Check if cell has any content (for has_data determination)
                if cell.value is not None and str(cell.value).strip():
                    has_any_data = True

                # Check if cell is translatable
                if self._is_translatable_cell(cell):
                    cell_address = (row, col)
                    translatable_cells.append(cell_address)

        # Create sheet entity
        excel_sheet = ExcelSheet(
            name=sheet_name,
            index=sheet_index,
            row_count=max_row,
            column_count=max_col,
            has_data=has_any_data,
            translatable_cells=translatable_cells
        )
        
        return excel_sheet
    
    def _is_translatable_cell(self, cell) -> bool:
        """Check if a cell contains translatable text."""
        try:
            if not cell.value:
                return False
            
            # Convert to string
            cell_text = str(cell.value).strip()
            
            if not cell_text:
                return False
            
            # Skip if it's purely numeric
            try:
                float(cell_text)
                return False
            except ValueError:
                pass
            
            # Skip if it's a formula
            if cell_text.startswith('='):
                return False
            
            # Check if it contains actual text characters
            if not self.text_pattern.search(cell_text):
                return False
            
            # Check if text is in brackets (should be preserved)
            for pattern in self.bracket_patterns:
                if pattern.fullmatch(cell_text):
                    return False
            
            return True
            
        except Exception:
            return False
    
    async def _load_xls_file(self, file_path: Path):
        """Load .xls file and convert to openpyxl workbook."""
        try:
            import xlrd
            import openpyxl
            
            # Read .xls file
            xls_workbook = xlrd.open_workbook(file_path)
            
            # Create new .xlsx workbook
            xlsx_workbook = openpyxl.Workbook()
            xlsx_workbook.remove(xlsx_workbook.active)  # Remove default sheet
            
            # Convert each sheet
            for sheet_index in range(xls_workbook.nsheets):
                xls_sheet = xls_workbook.sheet_by_index(sheet_index)
                xlsx_sheet = xlsx_workbook.create_sheet(title=xls_sheet.name)
                
                # Copy cell values
                for row in range(xls_sheet.nrows):
                    for col in range(xls_sheet.ncols):
                        cell_value = xls_sheet.cell_value(row, col)
                        if cell_value:
                            xlsx_sheet.cell(row=row+1, column=col+1, value=cell_value)
            
            return xlsx_workbook
            
        except ImportError:
            # xlrd not available
            return None
        except Exception:
            return None
    
    def extract_formatting(self, cell, worksheet=None) -> Formatting:
        """Extract formatting information from a cell."""
        try:
            # Extract merged cell information
            is_merged = False
            is_merged_top_left = False
            merged_bounds = None
            merged_range = None

            if worksheet:
                cell_coordinate = f"{get_column_letter(cell.column)}{cell.row}"
                for merged_range_obj in worksheet.merged_cells.ranges:
                    if cell_coordinate in merged_range_obj:
                        is_merged = True
                        merged_bounds = merged_range_obj.bounds
                        merged_range = str(merged_range_obj)
                        # Check if this is the top-left cell of the merged range
                        min_col, min_row, _, _ = merged_bounds
                        is_merged_top_left = (cell.row == min_row and cell.column == min_col)
                        break

            cell_dimensions = CellDimensions(
                row=cell.row,
                column=cell.column,
                is_merged=is_merged,
                is_merged_top_left=is_merged_top_left,
                merged_bounds=merged_bounds,
                merged_range=merged_range
            )

            formatting = Formatting(cell_dimensions=cell_dimensions)
            formatting.extract_from_cell(cell)

            return formatting

        except Exception:
            # Return basic formatting if extraction fails
            return Formatting(
                cell_dimensions=CellDimensions(row=cell.row, column=cell.column)
            )
    
    def apply_formatting(self, cell, formatting: Formatting) -> None:
        """Apply formatting to a cell."""
        try:
            formatting.apply_to_cell(cell)
        except Exception:
            # Silently ignore formatting errors
            pass
    
    async def get_translatable_text_with_formatting(self, excel_file: ExcelFile, selected_sheets: List[str] = None) -> List[Dict[str, Any]]:
        """Get translatable text with formatting information for selected sheets only."""
        translatable_data = []

        try:
            workbook = openpyxl.load_workbook(excel_file.file_path, data_only=False)

            for sheet in excel_file.sheets:
                # Skip sheets that are not selected
                if selected_sheets is not None and sheet.name not in selected_sheets:
                    continue

                if sheet.name not in workbook.sheetnames:
                    continue

                worksheet = workbook[sheet.name]

                for row, col in sheet.translatable_cells:
                    cell = worksheet.cell(row=row, column=col)

                    if self._is_translatable_cell(cell):
                        formatting = self.extract_formatting(cell, worksheet)

                        translatable_data.append({
                            'sheet_name': sheet.name,
                            'row': row,
                            'column': col,
                            'cell_address': f"{get_column_letter(col)}{row}",
                            'original_text': str(cell.value),
                            'formatting': formatting,
                            'cell_reference': cell
                        })

            return translatable_data

        except Exception:
            return []
    
    async def apply_translations_with_formatting(
        self,
        excel_file: ExcelFile,
        translations: Dict[str, str],
        output_path: Path,
        selected_sheets: List[str] = None
    ) -> bool:
        """Apply translations while preserving comprehensive formatting for selected sheets only."""
        try:
            workbook = openpyxl.load_workbook(excel_file.file_path, data_only=False)

            # Preserve workbook-level properties
            workbook_properties = self._extract_workbook_properties(workbook)

            # Get translatable data with formatting for selected sheets only
            translatable_data = await self.get_translatable_text_with_formatting(excel_file, selected_sheets)

            # Handle tables by temporarily removing them during translation
            # This prevents corruption when headers are translated
            preserved_tables = self._preserve_and_remove_tables(workbook)

            # Process only selected worksheets
            sheets_to_process = selected_sheets if selected_sheets is not None else workbook.sheetnames
            for sheet_name in sheets_to_process:
                if sheet_name not in workbook.sheetnames:
                    continue

                worksheet = workbook[sheet_name]

                # Preserve worksheet-level properties
                sheet_properties = self._extract_sheet_properties(worksheet)

                # Apply translations to this sheet
                sheet_translations = [data for data in translatable_data if data['sheet_name'] == sheet_name]

                for data in sheet_translations:
                    original_text = data['original_text']

                    if original_text in translations:
                        translated_text = translations[original_text]

                        # Handle merged cells properly
                        if self._is_merged_cell(worksheet, data['row'], data['column']):
                            self._handle_merged_cell_translation(worksheet, data, translated_text)
                        else:
                            # Regular cell translation
                            cell = worksheet.cell(row=data['row'], column=data['column'])
                            cell.value = translated_text

                            # Preserve formatting
                            if data['formatting']:
                                self.apply_formatting(cell, data['formatting'])

                # Restore tables with updated headers
                sheet_tables = preserved_tables.get(sheet_name, [])
                if sheet_tables:
                    self._restore_tables_with_translated_headers(worksheet, sheet_tables, translations)

                # Restore worksheet-level properties
                self._restore_sheet_properties(worksheet, sheet_properties)

            # Restore workbook-level properties
            self._restore_workbook_properties(workbook, workbook_properties)

            # No additional cleanup needed with the new approach

            # Save the workbook
            workbook.save(output_path)
            return True

        except Exception as e:
            print(f"Error applying translations: {e}")
            return False

    def _extract_workbook_properties(self, workbook):
        """Extract workbook-level properties for preservation."""
        try:
            properties = {
                'active_sheet': workbook.active.title if workbook.active else None,
                'sheet_order': list(workbook.sheetnames),
                'defined_names': {},
                'properties': {
                    'title': getattr(workbook.properties, 'title', None),
                    'creator': getattr(workbook.properties, 'creator', None),
                    'description': getattr(workbook.properties, 'description', None),
                    'subject': getattr(workbook.properties, 'subject', None),
                    'keywords': getattr(workbook.properties, 'keywords', None),
                    'category': getattr(workbook.properties, 'category', None),
                    'comments': getattr(workbook.properties, 'comments', None),
                }
            }

            # Extract defined names (named ranges)
            try:
                for name in workbook.defined_names:
                    if hasattr(name, 'name') and hasattr(name, 'value'):
                        properties['defined_names'][name.name] = str(name.value)
            except Exception:
                # Skip defined names if extraction fails
                pass

            return properties
        except Exception as e:
            # Log the specific error for debugging but don't fail
            print(f"Warning: Could not extract workbook properties: {e}")
            return {}

    def _restore_workbook_properties(self, workbook, properties):
        """Restore workbook-level properties."""
        try:
            if not properties:
                return

            # Restore workbook properties
            if 'properties' in properties:
                props = properties['properties']
                if props.get('title'):
                    workbook.properties.title = props['title']
                if props.get('creator'):
                    workbook.properties.creator = props['creator']
                if props.get('description'):
                    workbook.properties.description = props['description']
                if props.get('subject'):
                    workbook.properties.subject = props['subject']
                if props.get('keywords'):
                    workbook.properties.keywords = props['keywords']
                if props.get('category'):
                    workbook.properties.category = props['category']
                if props.get('comments'):
                    workbook.properties.comments = props['comments']

            # Restore active sheet
            if properties.get('active_sheet') and properties['active_sheet'] in workbook.sheetnames:
                workbook.active = workbook[properties['active_sheet']]

        except Exception:
            pass

    def _extract_sheet_properties(self, worksheet):
        """Extract worksheet-level properties for preservation."""
        try:
            properties = {
                'column_dimensions': {},
                'row_dimensions': {},
                'merged_cells': list(worksheet.merged_cells.ranges),
                'freeze_panes': worksheet.freeze_panes,
                'sheet_view': {
                    'zoom_scale': getattr(worksheet.sheet_view, 'zoomScale', 100),
                    'show_gridlines': getattr(worksheet.sheet_view, 'showGridLines', True),
                    'show_row_col_headers': getattr(worksheet.sheet_view, 'showRowColHeaders', True),
                },
                'page_setup': {
                    'orientation': worksheet.page_setup.orientation,
                    'paper_size': worksheet.page_setup.paperSize,
                    'fit_to_page': worksheet.page_setup.fitToPage,
                    'fit_to_width': worksheet.page_setup.fitToWidth,
                    'fit_to_height': worksheet.page_setup.fitToHeight,
                },
                'print_area': worksheet.print_area,
                'auto_filter': worksheet.auto_filter.ref if worksheet.auto_filter else None,
                'conditional_formatting': worksheet.conditional_formatting,
            }

            # Extract column dimensions
            for col_letter, col_dim in worksheet.column_dimensions.items():
                properties['column_dimensions'][col_letter] = {
                    'width': col_dim.width,
                    'hidden': col_dim.hidden,
                    'outline_level': col_dim.outline_level,
                    'collapsed': col_dim.collapsed,
                }

            # Extract row dimensions
            for row_num, row_dim in worksheet.row_dimensions.items():
                properties['row_dimensions'][row_num] = {
                    'height': row_dim.height,
                    'hidden': row_dim.hidden,
                    'outline_level': row_dim.outline_level,
                    'collapsed': row_dim.collapsed,
                }

            return properties
        except Exception:
            return {}

    def _restore_sheet_properties(self, worksheet, properties):
        """Restore worksheet-level properties."""
        try:
            if not properties:
                return

            # Restore column dimensions
            if 'column_dimensions' in properties:
                for col_letter, col_props in properties['column_dimensions'].items():
                    col_dim = worksheet.column_dimensions[col_letter]
                    if col_props.get('width'):
                        col_dim.width = col_props['width']
                    col_dim.hidden = col_props.get('hidden', False)
                    col_dim.outline_level = col_props.get('outline_level', 0)
                    col_dim.collapsed = col_props.get('collapsed', False)

            # Restore row dimensions
            if 'row_dimensions' in properties:
                for row_num, row_props in properties['row_dimensions'].items():
                    row_dim = worksheet.row_dimensions[row_num]
                    if row_props.get('height'):
                        row_dim.height = row_props['height']
                    row_dim.hidden = row_props.get('hidden', False)
                    row_dim.outline_level = row_props.get('outline_level', 0)
                    row_dim.collapsed = row_props.get('collapsed', False)

            # Restore freeze panes
            if properties.get('freeze_panes'):
                worksheet.freeze_panes = properties['freeze_panes']

            # Restore print area
            if properties.get('print_area'):
                worksheet.print_area = properties['print_area']

            # Restore auto filter
            if properties.get('auto_filter'):
                worksheet.auto_filter.ref = properties['auto_filter']

        except Exception:
            pass

    def _is_merged_cell(self, worksheet, row, column):
        """Check if a cell is part of a merged range."""
        try:
            cell_coordinate = f"{get_column_letter(column)}{row}"

            for merged_range in worksheet.merged_cells.ranges:
                if cell_coordinate in merged_range:
                    return True
            return False
        except Exception:
            return False

    def _handle_merged_cell_translation(self, worksheet, data, translated_text):
        """Handle translation of merged cells properly."""
        try:
            cell_coordinate = f"{get_column_letter(data['column'])}{data['row']}"

            # Find the merged range containing this cell
            for merged_range in worksheet.merged_cells.ranges:
                if cell_coordinate in merged_range:
                    # Get the top-left cell of the merged range
                    min_col, min_row, _, _ = merged_range.bounds
                    top_left_cell = worksheet.cell(row=min_row, column=min_col)

                    # Only update if this is the top-left cell (where content is stored)
                    if data['row'] == min_row and data['column'] == min_col:
                        top_left_cell.value = translated_text

                        # Preserve formatting
                        if data['formatting']:
                            self.apply_formatting(top_left_cell, data['formatting'])

                    break
        except Exception:
            # Fallback to regular cell handling
            cell = worksheet.cell(row=data['row'], column=data['column'])
            cell.value = translated_text
            if data['formatting']:
                self.apply_formatting(cell, data['formatting'])

    def _preserve_and_remove_tables(self, workbook) -> Dict[str, List[Dict]]:
        """Preserve table information and temporarily remove tables to prevent corruption."""
        preserved_tables = {}

        try:
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                sheet_tables = []

                # Check if worksheet has tables
                if hasattr(worksheet, 'tables') and worksheet.tables:
                    # Store table information
                    for table_name, table in list(worksheet.tables.items()):
                        # Handle different table object types
                        table_ref = None
                        table_display_name = table_name
                        table_style_info = None

                        if isinstance(table, str):
                            # Table is stored as a string (table range)
                            table_ref = table
                        else:
                            # Table is an object
                            table_ref = getattr(table, 'ref', None)
                            table_display_name = getattr(table, 'displayName', table_name)
                            table_style_info = getattr(table, 'tableStyleInfo', None)

                        # If we still don't have a range, try to auto-detect from data
                        if not table_ref:
                            table_ref = self._auto_detect_table_range(worksheet)

                        table_info = {
                            'name': table_name,
                            'ref': table_ref,
                            'displayName': table_display_name,
                            'tableStyleInfo': table_style_info,
                        }
                        sheet_tables.append(table_info)

                        # Remove table to prevent corruption during translation
                        del worksheet.tables[table_name]

                if sheet_tables:
                    preserved_tables[sheet_name] = sheet_tables

        except Exception as e:
            print(f"Warning: Could not preserve tables: {e}")

        return preserved_tables

    def _auto_detect_table_range(self, worksheet) -> str:
        """Auto-detect table range by finding data boundaries."""
        try:
            # Find the data boundaries
            max_row = 1
            max_col = 1

            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        max_row = max(max_row, cell.row)
                        max_col = max(max_col, cell.column)

            # Convert to Excel range format
            from openpyxl.utils import get_column_letter
            end_col_letter = get_column_letter(max_col)
            return f"A1:{end_col_letter}{max_row}"

        except Exception:
            # Fallback to a reasonable default
            return "A1:D10"

    def _restore_tables_with_translated_headers(self, worksheet, table_infos: List[Dict], translations: Dict[str, str]):
        """Restore tables with translated headers after translation is complete."""
        try:
            for table_info in table_infos:
                # Create new table with updated range if needed
                table_range = table_info.get('ref')
                if not table_range:
                    continue

                # Create new table
                from openpyxl.worksheet.table import Table, TableStyleInfo
                table = Table(
                    displayName=table_info.get('displayName', table_info['name']),
                    ref=table_range
                )

                # Restore table style if it existed
                if table_info.get('tableStyleInfo'):
                    table.tableStyleInfo = table_info['tableStyleInfo']
                else:
                    # Apply a default table style
                    style = TableStyleInfo(
                        name="TableStyleMedium9",
                        showFirstColumn=False,
                        showLastColumn=False,
                        showRowStripes=True,
                        showColumnStripes=True
                    )
                    table.tableStyleInfo = style

                # Add table back to worksheet
                worksheet.add_table(table)

        except Exception as e:
            print(f"Warning: Could not restore tables: {e}")


