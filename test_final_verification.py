#!/usr/bin/env python3
"""
Final Verification Script for Excel-T Translation System

This script performs comprehensive verification of the translation system
to ensure all functionality works correctly after our fixes.
"""

import os
import sys
import tempfile
import json
from openpyxl import Workbook, load_workbook

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from infrastructure.plugins.deepl_translator import DeepLTranslator
from gui.controllers.translation_controller import TranslationController
from infrastructure.file_handlers.excel_handler import ExcelHandler


class FinalVerificationTest:
    def __init__(self):
        self.passed_tests = 0
        self.total_tests = 0
        self.test_results = []
        
    def log_test(self, test_name, passed, details=""):
        """Log a test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "✓ PASS"
        else:
            status = "✗ FAIL"
        
        result = f"{status}: {test_name}"
        if details:
            result += f" - {details}"
        
        print(result)
        self.test_results.append({"test": test_name, "passed": passed, "details": details})
    
    def create_test_excel(self, filename, data):
        """Create a test Excel file with specified data"""
        wb = Workbook()
        ws = wb.active
        ws.title = "Test Sheet"
        
        for row_idx, row_data in enumerate(data, 1):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)
        
        wb.save(filename)
        return filename
    
    def test_deepl_translator_initialization(self):
        """Test 1: DeepL Translator Initialization"""
        try:
            translator = DeepLTranslator()
            self.log_test("DeepL Translator Initialization", True, "Translator initialized successfully")
            return translator
        except Exception as e:
            self.log_test("DeepL Translator Initialization", False, f"Error: {str(e)}")
            return None
    
    def test_language_code_mapping(self, translator):
        """Test 2: Language Code Mapping"""
        if not translator:
            self.log_test("Language Code Mapping", False, "No translator available")
            return
        
        # Test various language code formats
        test_cases = [
            ("English", "EN-US"),
            ("Japanese", "JA"),
            ("Spanish", "ES"),
            ("French", "FR"),
            ("German", "DE"),
            ("italian", "IT"),  # Test case insensitive
            ("CHINESE", "ZH"),  # Test uppercase
            ("Korean", "KO"),
        ]
        
        controller = TranslationController()
        passed = 0
        total = len(test_cases)
        
        for lang_name, expected_code in test_cases:
            try:
                result = controller._get_language_code(lang_name)
                if result == expected_code:
                    passed += 1
                else:
                    print(f"    Expected {expected_code} for {lang_name}, got {result}")
            except Exception as e:
                print(f"    Error mapping {lang_name}: {str(e)}")
        
        success_rate = (passed / total) * 100
        self.log_test("Language Code Mapping", passed == total, 
                     f"{passed}/{total} mappings correct ({success_rate:.1f}%)")
    
    def test_single_translation(self, translator):
        """Test 3: Single Text Translation"""
        if not translator:
            self.log_test("Single Text Translation", False, "No translator available")
            return
        
        try:
            test_text = "Hello, how are you today?"
            result = translator.translate_text(test_text, source_lang="EN", target_lang="JA")
            
            if result and result != test_text and len(result) > 0:
                self.log_test("Single Text Translation", True, 
                             f"'{test_text}' → '{result[:50]}...'")
            else:
                self.log_test("Single Text Translation", False, "Translation failed or unchanged")
        except Exception as e:
            self.log_test("Single Text Translation", False, f"Error: {str(e)}")
    
    def test_batch_translation(self, translator):
        """Test 4: Batch Translation"""
        if not translator:
            self.log_test("Batch Translation", False, "No translator available")
            return
        
        try:
            test_texts = [
                "Good morning",
                "How can I help you?",
                "Thank you very much",
                "Please wait a moment",
                "Have a nice day"
            ]
            
            results = translator.translate_batch(test_texts, source_lang="EN", target_lang="JA")
            
            if len(results) == len(test_texts):
                success_count = sum(1 for r in results if r and len(r) > 0)
                self.log_test("Batch Translation", success_count == len(test_texts),
                             f"{success_count}/{len(test_texts)} translations successful")
            else:
                self.log_test("Batch Translation", False, "Result count mismatch")
        except Exception as e:
            self.log_test("Batch Translation", False, f"Error: {str(e)}")
    
    def test_excel_file_processing(self):
        """Test 5: Excel File Processing"""
        try:
            # Create test Excel file
            test_data = [
                ["Product ID", "Product Name", "Description", "Category"],
                ["PROD-001", "Laptop Computer", "High-performance laptop for business", "Electronics"],
                ["PROD-002", "Office Chair", "Ergonomic office chair with lumbar support", "Furniture"],
                ["PROD-003", "Smartphone", "Latest model smartphone with advanced features", "Electronics"],
                ["PROD-004", "Desk Lamp", "LED desk lamp with adjustable brightness", "Lighting"]
            ]
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
                test_file = tmp_file.name
            
            self.create_test_excel(test_file, test_data)
            
            # Load and process the file
            handler = ExcelHandler()
            excel_file = handler.load_file(test_file)
            
            if excel_file and len(excel_file.sheets) > 0:
                sheet = excel_file.sheets[0]
                cell_count = sum(len(row.cells) for row in sheet.rows)
                self.log_test("Excel File Processing", True, 
                             f"Loaded {len(excel_file.sheets)} sheet(s), {cell_count} cells")
            else:
                self.log_test("Excel File Processing", False, "Failed to load Excel file")
            
            # Cleanup
            os.unlink(test_file)
            
        except Exception as e:
            self.log_test("Excel File Processing", False, f"Error: {str(e)}")
    
    def test_end_to_end_translation(self):
        """Test 6: End-to-End Translation Workflow"""
        try:
            # Create test Excel file
            test_data = [
                ["Item", "Description"],
                ["Book", "A comprehensive guide to programming"],
                ["Pen", "High-quality writing instrument"],
                ["Notebook", "Professional notebook for meetings"]
            ]
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_input:
                input_file = tmp_input.name
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_output:
                output_file = tmp_output.name
            
            self.create_test_excel(input_file, test_data)
            
            # Load file
            handler = ExcelHandler()
            excel_file = handler.load_file(input_file)
            
            # Initialize translator and controller
            translator = DeepLTranslator()
            controller = TranslationController()
            
            # Translate
            controller.translator = translator
            success = controller.translate_file(
                excel_file,
                source_language="English",
                target_language="Japanese",
                selected_sheets=["Test Sheet"]
            )
            
            if success:
                # Export
                handler.save_file(excel_file, output_file)
                
                # Verify export
                wb = load_workbook(output_file)
                ws = wb.active
                
                # Check if translations occurred (Japanese characters should be present)
                translated_text = ws.cell(row=2, column=2).value
                if translated_text and any('\u3040' <= char <= '\u309F' or '\u30A0' <= char <= '\u30FF' or '\u4E00' <= char <= '\u9FAF' for char in translated_text):
                    self.log_test("End-to-End Translation", True, 
                                 f"Translation complete: '{translated_text[:30]}...'")
                else:
                    self.log_test("End-to-End Translation", False, 
                                 f"No Japanese characters found in result: '{translated_text}'")
            else:
                self.log_test("End-to-End Translation", False, "Translation process failed")
            
            # Cleanup
            for file_path in [input_file, output_file]:
                if os.path.exists(file_path):
                    os.unlink(file_path)
            
        except Exception as e:
            self.log_test("End-to-End Translation", False, f"Error: {str(e)}")
    
    def test_long_text_handling(self, translator):
        """Test 7: Long Text Handling"""
        if not translator:
            self.log_test("Long Text Handling", False, "No translator available")
            return
        
        try:
            # Test with a long sentence
            long_text = """
            This is a comprehensive technical documentation that provides detailed information about 
            the installation procedures, configuration settings, system requirements, compatibility 
            matrix, troubleshooting guides, maintenance schedules, security protocols, backup 
            procedures, disaster recovery plans, and performance optimization techniques for 
            enterprise-level software deployment in production environments.
            """.strip()
            
            result = translator.translate_text(long_text, source_lang="EN", target_lang="JA")
            
            if result and result != long_text and len(result) > 50:
                self.log_test("Long Text Handling", True, 
                             f"Long text ({len(long_text)} chars) translated to {len(result)} chars")
            else:
                self.log_test("Long Text Handling", False, "Long text translation failed")
        except Exception as e:
            self.log_test("Long Text Handling", False, f"Error: {str(e)}")
    
    def test_error_handling(self):
        """Test 8: Error Handling"""
        try:
            controller = TranslationController()
            
            # Test invalid language
            result = controller._get_language_code("InvalidLanguage")
            if result == "EN-US":  # Should default to English
                self.log_test("Error Handling - Invalid Language", True, "Defaults to EN-US for invalid language")
            else:
                self.log_test("Error Handling - Invalid Language", False, f"Got {result} instead of EN-US")
            
            # Test empty string
            result = controller._get_language_code("")
            if result == "EN-US":  # Should default to English
                self.log_test("Error Handling - Empty Language", True, "Defaults to EN-US for empty language")
            else:
                self.log_test("Error Handling - Empty Language", False, f"Got {result} instead of EN-US")
            
        except Exception as e:
            self.log_test("Error Handling", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all verification tests"""
        print("=== Final Verification Test Suite ===")
        print("Testing all components of the Excel-T translation system...\n")
        
        # Run tests
        translator = self.test_deepl_translator_initialization()
        self.test_language_code_mapping(translator)
        self.test_single_translation(translator)
        self.test_batch_translation(translator)
        self.test_excel_file_processing()
        self.test_end_to_end_translation()
        self.test_long_text_handling(translator)
        self.test_error_handling()
        
        # Summary
        print(f"\n=== Test Summary ===")
        print(f"Tests Passed: {self.passed_tests}/{self.total_tests}")
        success_rate = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        print(f"Success Rate: {success_rate:.1f}%")
        
        if self.passed_tests == self.total_tests:
            print("🎉 ALL TESTS PASSED! The translation system is working correctly.")
        else:
            print("⚠️  Some tests failed. Review the details above.")
            failed_tests = [r for r in self.test_results if not r["passed"]]
            print("\nFailed Tests:")
            for test in failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        return self.passed_tests == self.total_tests


if __name__ == "__main__":
    test_suite = FinalVerificationTest()
    all_passed = test_suite.run_all_tests()
    sys.exit(0 if all_passed else 1)
