#!/bin/bash
# Reliable Excel Translator Startup Script

echo "🚀 Excel Translator - Reliable Startup"
echo "======================================"

# Navigate to script directory
cd "$(dirname "$0")"

# Check virtual environment
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "Please run: python -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Set environment variables
echo "🔑 Setting up environment..."
export DEEPL_API_KEY=65e0cb37-77da-44b0-b0f0-1b80ca38f9a8:fx

# Verify API key is set
echo "✅ DeepL API Key: ***${DEEPL_API_KEY: -4}"

# Start application
echo "🎯 Starting Excel Translator..."
echo ""

# Try the launcher first, fallback to main.py
if [ -f "launch_app.py" ]; then
    python launch_app.py
else
    python main.py
fi

echo ""
echo "Application closed."
