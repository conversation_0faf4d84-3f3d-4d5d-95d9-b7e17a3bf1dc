---
type: "agent_requested"
description: "when generating the code using agent"
---
# Develop an App based on Functional and Non-Functional requirements using Qt and other modules (following by requirements.txt):


### Functional Requirements List

#### File Handling
- Users should be able to upload Excel file via a Qt-based GUI.

> Qt is a cross-platform application development framework used for creating graphical user interfaces (GUIs).

- The system should support Excel formats `.xlsx` and `.xls`.
- The system should automatically detect all sheets within each uploaded Excel file.
- The system should automatically detect text cells that require translation.
- The system should support using external configuration file (such as JSON or Excel file) to define the file and sheet names to be translated.

#### Language Management
- Users should be able to manually select the source and target languages (e.g., Japanese → Vietnamese or Vietnamese → Japanese).
- The system should support automatic detection of the source language via translation API.

#### Translation Logic
- The system should be able to send text batches to translation APIs (Google or DeepL).
- The system should be able to translate content across multiple sheets of file simultaneously.
- The system should be able to apply the translated content to the correct location within the Excel file.
- The system should be able to ignore and preserve text enclosed in `[]` (square brackets) or `「 」` (Japanese quotation marks).
- The system should preserve the original Excel file structure, design, format, style, theme, including sheet order and names, cell formatting (font, color, size, bold/italic, borders), merged cells, column widths and row heights, frozen panes, and cell comments or notes (if present).

#### Output Generation
- The system should be able to generate translated Excel file that retain the original design and formatting.
- Translated file should be downloadable via the UI.

#### User Interface
 - The UI should include a file upload area (with optional drag-and-drop support), language selectors (source and target), a translation start button, and a download/export button.
- While translating, the UI should display a real-time progress bar or indicator.
- Clear user feedback (errors, success messages, logs) should be displayed.

---

### Non-Functional Requirements List

#### Performance
- Excel file with 10,000+ rows should be processed and translated within 60 seconds (depending on API rate limits for free plans).

> Example:
>> Batch size: 50  
>> Maximum requests in 60 seconds: 5 × 60 = 300 requests  
>> Maximum rows: 300 × 50 = 15,000 rows

- The system should use intelligent batching to reduce latency and excessive API usage.
- Background tasks must not freeze the UI.

#### Security
- API keys should be securely stored using environment variables or encrypted configuration file.
- File processing should occur locally, and only raw text should be sent to the API.
- File or translations must not be stored or logged permanently.

#### Scalability
- The system should support large Excel file with minimal performance degradation.
- A plug-in-based architecture should allow easy addition of new APIs or file types (e.g., Hexagonal Architecture).

#### Availability & Issue Handling
- Errors (e.g., API failures, invalid file, network issues) should be logged and displayed to users.
- The system should automatically detect known issues (e.g., missing configuration, API errors, file format mismatches).
- A startup health check should validate API credentials, internet connectivity, and configuration file availability.

#### Maintainability
- The architecture should allow separation of GUI, application logic, file handler, and API plugins.
- Logic should be modular and unit-testable.

#### Logging & Operation History
- The system should log details such as file uploads, translated sheets, errors, warnings, retries, and processing time per file/sheet.

#### Usability
- The GUI should be intuitive and usable even by non-technical users.
- Supported interface languages should include Japanese, Vietnamese, and optionally English.
- Errors should be user-friendly and understandable (e.g., "File not found" instead of a traceback).

#### Portability
- The application should run natively on both Windows and macOS.
