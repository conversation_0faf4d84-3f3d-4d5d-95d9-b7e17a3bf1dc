---
type: "agent_requested"
description: "when generating the code using agent"
---
# Follow this following file structure to develop the project:

```bash
Excel-T/
├── main.py
├── requirements.txt
├── setup.py
├── pyproject.toml
├── .env
├── .env.example
├── .gitignore
├── README.md
├── LICENSE
├── Dockerfile
├── docker-compose.yml
├── Makefile
│
├── src/
│   ├── __init__.py
│   │
│   ├── domain/
│   │   ├── __init__.py
│   │   │
│   │   ├── entities/
│   │   │   ├── __init__.py
│   │   │   ├── base_entity.py
│   │   │   ├── file_entity.py
│   │   │   ├── sheet_entity.py
│   │   │   ├── cell_entity.py
│   │   │   ├── translation_entity.py
│   │   │   ├── translation_job_entity.py
│   │   │   ├── user_settings_entity.py
│   │   │   └── api_key_entity.py
│   │   │
│   │   ├── value_objects/
│   │   │   ├── __init__.py
│   │   │   ├── locale.py
│   │   │   ├── language_code.py
│   │   │   ├── file_path.py
│   │   │   ├── file_format.py
│   │   │   ├── cell_coordinate.py
│   │   │   ├── cell_format.py
│   │   │   ├── translation_config.py
│   │   │   ├── api_provider.py
│   │   │   ├── progress_status.py
│   │   │   └── error_code.py
│   │   │
│   │   ├── enums/
│   │   │   ├── __init__.py
│   │   │   ├── language_enum.py
│   │   │   ├── translator_enum.py
│   │   │   ├── file_format_enum.py
│   │   │   ├── translation_status_enum.py
│   │   │   ├── progress_status_enum.py
│   │   │   └── error_type_enum.py
│   │   │
│   │   ├── repositories/
│   │   │   ├── __init__.py
│   │   │   ├── file_repository.py
│   │   │   ├── translation_repository.py
│   │   │   ├── settings_repository.py
│   │   │   └── localization_repository.py
│   │   │
│   │   ├── events/
│   │   │   ├── __init__.py
│   │   │   ├── base_event.py
│   │   │   ├── translation_started_event.py
│   │   │   ├── translation_completed_event.py
│   │   │   ├── translation_failed_event.py
│   │   │   ├── file_loaded_event.py
│   │   │   ├── settings_changed_event.py
│   │   │   └── language_changed_event.py
│   │   │
│   │   └── exceptions/
│   │       ├── __init__.py
│   │       ├── base_exception.py
│   │       ├── file_not_found_exception.py
│   │       ├── invalid_file_format_exception.py
│   │       ├── translation_failed_exception.py
│   │       ├── invalid_language_exception.py
│   │       ├── api_key_invalid_exception.py
│   │       ├── validation_exception.py
│   │       └── business_rule_violation_exception.py
│   │
│   ├── application/
│   │   ├── __init__.py
│   │   │
│   │   ├── use_cases/
│   │   │   ├── __init__.py
│   │   │   ├── base_use_case.py
│   │   │   │
│   │   │   ├── translation/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── translate_file_use_case.py
│   │   │   │   ├── translate_text_use_case.py
│   │   │   │   ├── batch_translate_use_case.py
│   │   │   │   ├── validate_translation_use_case.py
│   │   │   │   └── cancel_translation_use_case.py
│   │   │   │
│   │   │   ├── file/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── load_file_use_case.py
│   │   │   │   ├── save_file_use_case.py
│   │   │   │   ├── export_file_use_case.py
│   │   │   │   └── validate_file_use_case.py
│   │   │   │
│   │   │   ├── settings/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── update_settings_use_case.py
│   │   │   │   └── validate_api_keys_use_case.py
│   │   │   │
│   │   │   └── localization/
│   │   │       ├── __init__.py
│   │   │       ├── change_language_use_case.py
│   │   │       ├── get_translations_use_case.py
│   │   │       └── update_translations_use_case.py
│   │   │
│   │   ├── dto/
│   │   │   ├── __init__.py
│   │   │   ├── base.py
│   │   │   │
│   │   │   ├── request/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base_request.py
│   │   │   │   ├── file_upload_request.py
│   │   │   │   ├── translation_request.py
│   │   │   │   ├── batch_translation_request.py
│   │   │   │   ├── file_load_request.py
│   │   │   │   ├── file_save_request.py
│   │   │   │   ├── file_export_request.py
│   │   │   │   ├── file_validation_request.py
│   │   │   │   ├── settings_update_request.py
│   │   │   │   ├── api_key_validation_request.py
│   │   │   │   ├── language_change_request.py
│   │   │   │   └── translation_cancel_request.py
│   │   │   │
│   │   │   └── response/
│   │   │       ├── __init__.py
│   │   │       ├── base_response.py
│   │   │       ├── file_upload_response.py
│   │   │       ├── translation_response.py
│   │   │       ├── batch_translation_response.py
│   │   │       ├── file_load_response.py
│   │   │       ├── file_save_response.py
│   │   │       ├── file_export_response.py
│   │   │       ├── file_validation_response.py
│   │   │       ├── validation_result_response.py
│   │   │       ├── settings_response.py
│   │   │       ├── api_key_validation_response.py
│   │   │       ├── language_change_response.py
│   │   │       ├── translations_response.py
│   │   │       ├── progress_response.py
│   │   │       ├── error_response.py
│   │   │       └── success_response.py
│   │   │
│   │   ├── ports/
│   │   │   ├── __init__.py
│   │   │   │
│   │   │   ├── input/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── translation_input_port.py
│   │   │   │   ├── file_input_port.py
│   │   │   │   ├── settings_input_port.py
│   │   │   │   └── localization_input_port.py
│   │   │   │
│   │   │   └── output/
│   │   │       ├── __init__.py
│   │   │       ├── translation_output_port.py
│   │   │       ├── file_output_port.py
│   │   │       ├── notification_output_port.py
│   │   │       ├── progress_output_port.py
│   │   │       ├── logging_output_port.py
│   │   │       └── event_output_port.py
│   │   │
│   │   ├── handlers/
│   │   │   ├── __init__.py
│   │   │   ├── translation_event_handler.py
│   │   │   ├── file_event_handler.py
│   │   │   ├── settings_event_handler.py
│   │   │   ├── notification_handler.py
│   │   │   └── command_handler.py
│   │   │
│   │   ├── queries/
│   │   │   ├── __init__.py
│   │   │   ├── base_query.py
│   │   │   ├── get_supported_languages_query.py
│   │   │   ├── get_file_info_query.py
│   │   │   └── get_settings_query.py
│   │   │
│   │   └── validators/
│   │       ├── __init__.py
│   │       ├── base_validator.py
│   │       ├── translation_validator.py
│   │       ├── file_validator.py
│   │       ├── settings_validator.py
│   │       └── api_key_validator.py
│   │
│   ├── infrastructure/
│   │   ├── __init__.py
│   │   │
│   │   ├── adapters/
│   │   │   ├── __init__.py
│   │   │   │
│   │   │   ├── persistence/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── json_file_repository.py
│   │   │   │   ├── settings_repository_impl.py
│   │   │   │   └── localization_repository_impl.py
│   │   │   │
│   │   │   └── security/
│   │   │       ├── __init__.py
│   │   │       ├── encryption_adapter.py
│   │   │       ├── key_manager.py
│   │   │       └── secure_storage.py
│   │   │
│   │   ├── file_handlers/
│   │   │   ├── __init__.py
│   │   │   ├── base_file_handler.py
│   │   │   ├── excel_handler.py
│   │   │   └── file_handler_factory.py
│   │   │
│   │   ├── plugins/
│   │   │   ├── __init__.py
│   │   │   ├── google_translator.py
│   │   │   ├── deepl_translator.py
│   │   │   └── translation_plugin_factory.py
│   │   │
│   │   ├── config/
│   │   │   ├── __init__.py
│   │   │   ├── app_config.py
│   │   │   ├── api_config.py
│   │   │   ├── logging_config.py
│   │   │   ├── security_config.py
│   │   │   └── dependency_container.py
│   │   │
│   │   └── utils/
│   │       ├── __init__.py
│   │       ├── file_utils.py
│   │       ├── string_utils.py
│   │       ├── validation_utils.py
│   │       ├── crypto_utils.py
│   │       ├── async_utils.py
│   │       └── performance_utils.py
│   │
│   └── interfaces/
│       ├── __init__.py
│       │
│       ├── controllers/
│       │   ├── __init__.py
│       │   ├── base_controller.py
│       │   ├── translation_controller.py
│       │   ├── file_controller.py
│       │   ├── settings_controller.py
│       │   ├── localization_controller.py
│       │   └── validation_controller.py
│       │
│       └── presenters/
│           ├── __init__.py
│           ├── base_presenter.py
│           ├── translation_presenter.py
│           ├── progress_presenter.py
│           ├── error_presenter.py
│           ├── file_presenter.py
│           └── settings_presenter.py
│       
├── gui/
│   ├── __init__.py
│   │
│   ├── components/
│   │   ├── __init__.py
│   │   ├── base/
│   │   │   ├── __init__.py
│   │   │   ├── base_widget.py
│   │   │   ├── base_dialog.py
│   │   │   └── base_window.py
│   │   │
│   │   ├── ui_component/
│   │   │   ├── __init__.py
│   │   │   ├── file_selector.py
│   │   │   ├── language_selector.py
│   │   │   ├── progress_bar.py
│   │   │   ├── notification_widget.py
│   │   │   ├── table_widget.py
│   │   │   ├── settings_widget.py
│   │   │   ├── combobox.py
│   │   │   ├── buttons.py
│   │   │   ├── input_box.py
│   │   │   ├── scroll_bar.py
│   │   │   ├── checkbox.py
│   │   │   ├── scroll_area.py
│   │   │   ├── focus_frame.py
│   │   │   ├── dropdown.py
│   │   │   ├── logger_widget.py
│   │   │   └── status_widget.py
│   │   │
│   │   ├── translation/
│   │   │   ├── __init__.py
│   │   │   ├── translation_panel.py
│   │   │   ├── preview_panel.py
│   │   │   └── batch_panel.py
│   │   │
│   │   └── file/
│   │       ├── __init__.py
│   │       ├── file_explorer.py
│   │       ├── file_info_panel.py
│   │       ├── sheet_tabs.py
│   │       └── cell_editor.py
│   │
│   ├── windows/
│   │   ├── __init__.py
│   │   ├── main_window.py
│   │   ├── splash_window.py
│   │   ├── about_window.py
│   │   └── help_window.py
│   │
│   ├── dialogs/
│   │   ├── __init__.py
│   │   ├── settings_dialog.py
│   │   ├── api_keys_dialog.py
│   │   ├── language_dialog.py
│   │   ├── progress_dialog.py
│   │   ├── error_dialog.py
│   │   ├── confirmation_dialog.py
│   │   ├── file_format_dialog.py
│   │   └── export_dialog.py
│   │
│   ├── styles/
│   │   ├── __init__.py
│   │   ├── themes/
│   │   │   ├── __init__.py
│   │   │   ├── light_theme.py
│   │   │   ├── dark_theme.py
│   │   │   ├── blue_theme.py
│   │   │   └── custom_theme.py
│   │   │
│   │   ├── qss/
│   │   │   ├── base/
│   │   │   │   ├── base_widget.qss
│   │   │   │   ├── base_dialog.qss
│   │   │   │   └── base_window.qss
│   │   │   │
│   │   │   ├── components/
│   │   │   │   ├── file_selector.qss
│   │   │   │   ├── language_selector.qss
│   │   │   │   ├── progress_bar.qss
│   │   │   │   ├── notification_widget.qss
│   │   │   │   ├── table_widget.qss
│   │   │   │   ├── settings_widget.qss
│   │   │   │   ├── combobox.qss
│   │   │   │   ├── buttons.qss
│   │   │   │   ├── input_box.qss
│   │   │   │   ├── scroll_bar.qss
│   │   │   │   ├── checkbox.qss
│   │   │   │   ├── scroll_area.qss
│   │   │   │   ├── focus_frame.qss
│   │   │   │   ├── dropdown.qss
│   │   │   │   ├── logger_widget.qss
│   │   │   │   └── status_widget.qss
│   │   │   │
│   │   │   ├── translation/
│   │   │   │   ├── translation_panel.qss
│   │   │   │   ├── preview_panel.qss
│   │   │   │   └── batch_panel.qss
│   │   │   │
│   │   │   ├── file/
│   │   │   │   ├── file_info_panel.qss
│   │   │   │   ├── sheet_tabs.qss
│   │   │   │   └── cell_editor.qss
│   │   │   │
│   │   │   ├── windows/
│   │   │   │   ├── main_window.qss
│   │   │   │   ├── splash_window.qss
│   │   │   │   ├── about_window.qss
│   │   │   │   └── help_window.qss
│   │   │   │
│   │   │   ├── dialogs/
│   │   │   │   ├── settings_dialog.qss
│   │   │   │   ├── api_keys_dialog.qss
│   │   │   │   ├── language_dialog.qss
│   │   │   │   ├── progress_dialog.qss
│   │   │   │   ├── error_dialog.qss
│   │   │   │   ├── confirmation_dialog.qss
│   │   │   │   ├── file_format_dialog.qss
│   │   │   │   └── export_dialog.qss
│   │   │   │
│   │   │   └── themes/
│   │   │       ├── light/
│   │   │       │   ├── common.qss
│   │   │       │   ├── variables.qss
│   │   │       │   └── overrides.qss
│   │   │       │
│   │   │       ├── dark/
│   │   │       │   ├── common.qss
│   │   │       │   ├── variables.qss
│   │   │       │   └── overrides.qss
│   │   │       │
│   │   │       ├── blue/
│   │   │       │   ├── common.qss
│   │   │       │   ├── variables.qss
│   │   │       │   └── overrides.qss
│   │   │       │
│   │   │       └── custom/
│   │   │           ├── common.qss
│   │   │           ├── variables.qss
│   │   │           └── overrides.qss
│   │   │
│   │   ├── icons.py
│   │   └── fonts.py
│   │
│   ├── managers/
│   │   ├── __init__.py
│   │   ├── window_manager.py
│   │   ├── theme_manager.py
│   │   ├── layout_manager.py
│   │   ├── keyboard_manager.py
│   │   ├── clipboard_manager.py
│   │   ├── session_manager.py
│   │   └── localization_manager.py
│   │
│   ├── events/
│   │   ├── __init__.py
│   │   ├── event_dispatcher.py
│   │   ├── keyboard_events.py
│   │   ├── mouse_events.py
│   │   ├── file_events.py
│   │   └── window_events.py
│   │
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── gui_utils.py
│   │   ├── layout_utils.py
│   │   ├── widget_utils.py
│   │   ├── image_utils.py
│   │   └── animation_utils.py
│   │
│   └── models/
│       ├── __init__.py
│       ├── file_model.py
│       ├── translation_model.py
│       ├── settings_model.py
│       └── language_model.py
│
├── config/
│   ├── __init__.py
│   ├── settings.py
│   └── app_settings.json
│
├── helpers/
│   ├── __init__.py
│   └── logger.py
│
├── resources/
│   ├── translations/
│   │   ├── en.json
│   │   ├── ja.json
│   │   └── vi.json
│   │
│   ├── localization/
│   │   ├── en.json
│   │   ├── ja.json
│   │   └── vi.json
│   │
│   ├── assets/
│   │   ├── icons/
│   │   ├── images/
│   │   └── fonts/
│   │
│   ├── templates/
│   ├── schemas/
│   └── sample_files/
│
├── docs/
│   ├── USER_MANUAL.md
│   ├── CONFIGURATION.md
│   ├── INSTALLATION_MAC.md
│   └── INSTALLATION_WINDOWS.md
│
└── tests/
    ├── __init__.py
    ├── conftest.py
    ├── pytest.ini
    │
    ├── unit/
    │   ├── __init__.py
    │   │
    │   ├── domain/
    │   │   ├── __init__.py
    │   │   ├── entities/
    │   │   ├── value_objects/
    │   │   └── exceptions/
    │   │
    │   ├── application/
    │   │   ├── __init__.py
    │   │   ├── use_cases/
    │   │   ├── dto/
    │   │   ├── handlers/
    │   │   └── validators/
    │   │
    │   ├── infrastructure/
    │   │   ├── __init__.py
    │   │   ├── adapters/
    │   │   ├── file_handlers/
    │   │   ├── plugins/
    │   │   ├── config/
    │   │   └── utils/
    │   │
    │   ├── interfaces/
    │   │   ├── __init__.py
    │   │   ├── controllers/
    │   │   ├── presenters/
    │   │   └── serializers/
    │   │
    │   ├── gui/
    │   │   ├── __init__.py
    │   │   ├── components/
    │   │   ├── windows/
    │   │   ├── dialogs/
    │   │   └── managers/
    │   │
    │   ├── config/
    │   │   ├── __init__.py
    │   │   └── test_settings.py
    │   │
    │   └── helpers/
    │       ├── __init__.py
    │       └── test_logger.py
    │
    └── integration/
        ├── __init__.py
        ├── test_file_translation_workflow.py
        ├── test_settings_persistence.py
        ├── test_translation_service_integration.py
        ├── test_gui_application_flow.py
        ├── test_event_system_integration.py
        ├── test_localization_system.py
        ├── test_security_integration.py
        └── test_performance_integration.py
```